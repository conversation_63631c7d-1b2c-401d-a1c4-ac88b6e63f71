// Test script to diagnose Vercel deployment issues
// Run this locally to test your deployed Vercel app

const https = require('https');

// Replace with your actual Vercel app URL
const VERCEL_APP_URL = 'https://your-app-name.vercel.app';

async function testEndpoint(path, description) {
    return new Promise((resolve) => {
        console.log(`\n🔍 Testing: ${description}`);
        console.log(`📡 URL: ${VERCEL_APP_URL}${path}`);
        
        const req = https.get(`${VERCEL_APP_URL}${path}`, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                console.log(`📊 Status: ${res.statusCode}`);
                
                try {
                    const jsonData = JSON.parse(data);
                    console.log(`✅ Response:`, JSON.stringify(jsonData, null, 2));
                } catch (e) {
                    console.log(`📄 Raw Response:`, data);
                }
                
                resolve({ status: res.statusCode, data });
            });
        });
        
        req.on('error', (error) => {
            console.log(`❌ Request Error:`, error.message);
            resolve({ error: error.message });
        });
        
        req.setTimeout(30000, () => {
            console.log(`⏰ Request Timeout`);
            req.destroy();
            resolve({ error: 'Timeout' });
        });
    });
}

async function runTests() {
    console.log('🚀 Testing Vercel Deployment');
    console.log('=' .repeat(50));
    
    // Test 1: Basic Health Check
    await testEndpoint('/api/health', 'Basic Health Check');
    
    // Test 2: Debug Health Check
    await testEndpoint('/api/debug/health', 'Detailed Debug Health Check');
    
    // Test 3: Search Test
    await testEndpoint('/api/search?q=**********', 'Search for Test User');
    
    // Test 4: User Profile Test
    await testEndpoint('/api/user/**********', 'User Profile Test');
    
    console.log('\n🎯 Test Complete');
    console.log('=' .repeat(50));
    console.log('\n📋 Next Steps:');
    console.log('1. Check the error messages above');
    console.log('2. Verify environment variables in Vercel dashboard');
    console.log('3. Check MongoDB Atlas IP whitelist');
    console.log('4. Review Vercel function logs');
}

// Instructions
console.log('🔧 VERCEL DEPLOYMENT TEST SCRIPT');
console.log('=' .repeat(50));
console.log('📝 Instructions:');
console.log('1. Replace VERCEL_APP_URL with your actual Vercel app URL');
console.log('2. Run: node test-vercel-deployment.js');
console.log('3. Review the detailed error messages');
console.log('=' .repeat(50));

// Run tests if URL is updated
if (VERCEL_APP_URL !== 'https://your-app-name.vercel.app') {
    runTests();
} else {
    console.log('\n⚠️  Please update VERCEL_APP_URL with your actual Vercel app URL first!');
}
