﻿# Enhanced Referral Chain Visualizer

A powerful standalone web application for visualizing and analyzing referral chains from Telegram bot databases. Built with Node.js, Express, and MongoDB with advanced date-based filtering and analysis capabilities.

##  Features

### **Core Functionality**
- **Interactive Referral Chain Visualization**: Tree-like display of referral relationships
- **Advanced User Search**: Find users by ID, name, or username with intelligent matching
- **Comprehensive User Profiles**: Detailed user information with financial data and activity tracking
- **Real-time Statistics**: Live database statistics and user metrics

### ** Enhanced Date Analysis**
- **Date-Based User Analysis**: Analyze users who joined on specific dates
- **Quick Presets**: Today, Yesterday, Last 7 Days, Last 30 Days analysis
- **Custom Date Ranges**: Select any start and end date for analysis
- **Comprehensive Reports**: Summary statistics, top performers, and detailed user lists

### ** Individual User Referral Filtering**
- **Date-Specific Referral Filter**: See exactly which referrals were made on specific dates
- **Smart Count Display**: Shows " X of Y referrals\ when filtered
- **Maintain Full Functionality**: Easy toggle between filtered and all referrals
- **Tree Integration**: Filter indicators in referral chain visualization

### ** Advanced Analytics**
- **Export Functionality**: Download analysis data as CSV or JSON
- **Pagination Support**: Handle large datasets efficiently
- **Sorting Options**: Multiple sort criteria for detailed analysis
- **Real-time Updates**: Always current data from MongoDB

### ** User Experience**
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Professional UI**: Clean, intuitive interface with visual feedback
- **Fast Performance**: Optimized queries for large datasets
- **Error Handling**: Graceful fallbacks and user-friendly messages

## Prerequisites

- Node.js 18+ 
- MongoDB database with user collection
- Environment variables for database connection

## Installation & Deployment

### **Vercel Deployment**
1. Push the project to GitHub
2. Connect to Vercel
3. Set environment variables in Vercel dashboard:
 - MONGODB_URI: Your MongoDB connection string
 - DATABASE_NAME: Your database name
4. Deploy automatically

### **Local Development**
1. Clone or download this project
2. Install dependencies: 
pm install
3. Create a .env file with your MongoDB connection details
4. Start the application: 
pm start
5. Open your browser and navigate to http://localhost:3001

## Usage Guide

### **Date Analysis Dashboard**
1. Click \Date Analysis\ in the footer
2. Choose analysis type: Single Date, Date Range, or Quick Presets
3. View comprehensive results with summary statistics
4. Export data for further analysis

### **Individual User Referral Filtering**
1. Search for any user by ID, name, or username
2. Click \View Profile\ to open detailed user view
3. Navigate to \Referrals\ tab
4. Use date picker to select specific date
5. Click \Apply Filter\ to see referrals made on that date

## API Endpoints

### **Core Endpoints**
- GET /api/health - Health check and database status
- GET /api/search?q=query - Search for users
- GET /api/user/:userId - Get detailed user information
- GET /api/referral-chain/:userId - Get referral chain data

### ** Enhanced Analysis Endpoints**
- GET /api/analysis/date/:date - Analyze users who joined on specific date
- GET /api/analysis/range?start=date&end=date - Analyze users in date range
- GET /api/analysis/preset/:preset - Quick preset analysis
- GET /api/user/:userId/referrals?date=date - Get user referrals filtered by date

## Environment Variables

- MONGODB_URI - MongoDB connection string
- DATABASE_NAME - Name of the database to use
- PORT - Port to run the server on (default: 3001)

## Perfect For

- **Referral Campaign Analysis**: Track effectiveness of promotional campaigns
- **Daily Performance Monitoring**: Analyze referral activity for specific dates
- **User Behavior Analysis**: Understand referral patterns and trends
- **Administrative Oversight**: Comprehensive user management and analysis
- **Data Export & Reporting**: Generate detailed reports for stakeholders

## License

MIT

---

**Enhanced Referral Chain Visualizer - Providing unprecedented insight into referral networks with advanced date-based analysis and filtering capabilities.**
