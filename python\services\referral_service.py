"""
Referral service for managing referral system
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from telegram import Bot

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from models.user import UserModel
from utils.helpers import get_current_timestamp, send_safe_message

logger = logging.getLogger(__name__)

class ReferralService:
    """Service for referral-related operations"""
    
    def __init__(self):
        self.bot = Bot(settings.BOT_TOKEN)
    
    async def get_user_id_by_custom_parameter(self, custom_param: str) -> Optional[int]:
        """Get user ID by custom referral parameter"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            custom_referral = await collection.find_one({"custom_param": custom_param})
            
            if custom_referral:
                return custom_referral['user_id']
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user ID by custom parameter {custom_param}: {e}")
            return None

    async def track_custom_referral_usage(self, custom_param: str, action: str = 'click') -> bool:
        """Track custom referral usage (click or conversion)"""
        try:
            from services.custom_referral_service import CustomReferralService
            custom_referral_service = CustomReferralService()

            if action == 'click':
                return await custom_referral_service.track_custom_referral_click(custom_param)
            elif action == 'conversion':
                return await custom_referral_service.track_custom_referral_conversion(custom_param)

            return False

        except Exception as e:
            logger.error(f"Error tracking custom referral usage: {e}")
            return False
    
    async def create_custom_referral_link(self, user_id: int, custom_param: str) -> bool:
        """Create custom referral link - DEPRECATED: Use CustomReferralService instead"""
        try:
            # Delegate to the new service
            from services.custom_referral_service import CustomReferralService
            custom_referral_service = CustomReferralService()
            result = await custom_referral_service.create_custom_referral(custom_param, user_id, user_id)
            return result.get('success', False)

        except Exception as e:
            logger.error(f"Error creating custom referral link: {e}")
            return False
    
    async def update_referral_reward(
        self, 
        referrer_id: int, 
        referred_user_id: int, 
        amount: int, 
        referred_name: str
    ) -> bool:
        """Update referral reward for referrer"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Get referrer user
            referrer = await users_collection.find_one({"user_id": referrer_id})
            if not referrer:
                logger.error(f"Referrer {referrer_id} not found")
                return False
            
            # Add to referrer's balance
            new_balance = referrer.get('balance', 0) + amount
            
            # Add promotion report entry
            promotion_entry = {
                "referred_user_name": referred_name,
                "referred_user_id": referred_user_id,
                "amount_got": amount
            }
            
            # Update referrer
            result = await users_collection.update_one(
                {"user_id": referrer_id},
                {
                    "$set": {
                        "balance": new_balance,
                        "updated_at": get_current_timestamp()
                    },
                    "$push": {
                        "promotion_report": promotion_entry
                    }
                }
            )
            
            if result.modified_count > 0:
                # Send notification to referrer
                await self._send_referral_reward_notification(
                    referrer_id, referred_name, amount
                )

                # Update custom referral usage count if applicable
                await self._update_custom_referral_usage(referred_user_id)

                logger.info(f"Referral reward processed: {referrer_id} earned ₹{amount} for referring {referred_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating referral reward: {e}")
            return False
    
    async def _send_referral_reward_notification(
        self,
        referrer_id: int,
        referred_name: str,
        amount: int
    ) -> None:
        """Send referral reward notification to referrer (matching PHP exactly)"""
        try:
            # Get updated referrer data to show current balance
            from services.user_service import UserService
            user_service = UserService()
            referrer_user = await user_service.get_user(referrer_id)

            if not referrer_user:
                logger.error(f"Referrer {referrer_id} not found for notification")
                return

            current_balance = referrer_user.get('balance', 0)

            # Use exact PHP message format with proper balance formatting
            # Format balance to show .00 for whole numbers, or actual decimals for decimal numbers
            if isinstance(current_balance, (int, float)) and current_balance == int(current_balance):
                balance_display = f"{int(current_balance)}.00"
            else:
                balance_display = f"{current_balance:.2f}"

            message = f"🎉Invite {referred_name} 🔍  successfully! You get <b>₹{amount}! 🎁\n\n💵Balance:₹{balance_display}</b>"

            # Create keyboard with invite friends and my wallet buttons (matching PHP exactly)
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            from config.settings import settings

            # Get user display bonus for share message
            user_display_bonus = getattr(settings, 'USER_DISPLAY_BONUS_MAX', 100)
            referral_link = referrer_user.get('referral_link', '')

            share_text = f"{referral_link}\nI've Got Up To ₹{user_display_bonus}! Click URL To Join & Make Money Now!"
            share_url = f"https://t.me/share/url?text={share_text}"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('👥 Invite friends', url=share_url)],
                [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
            ])

            await send_safe_message(
                self.bot,
                referrer_id,
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error sending referral reward notification: {e}")
    
    async def _update_custom_referral_usage(self, referred_user_id: int) -> None:
        """Update custom referral usage count"""
        try:
            # Get the referred user to check their referral source
            users_collection = await get_collection(COLLECTIONS['users'])
            referred_user = await users_collection.find_one({"user_id": referred_user_id})

            if not referred_user:
                return

            referred_by = referred_user.get('referred_by', 'None')
            if referred_by == 'None' or referred_by.isdigit():
                return  # Not a custom referral

            # Use the new custom referral service
            from services.custom_referral_service import CustomReferralService
            custom_referral_service = CustomReferralService()
            await custom_referral_service.track_custom_referral_conversion(referred_by)

        except Exception as e:
            logger.error(f"Error updating custom referral usage: {e}")
    
    async def get_custom_referral_links(self, user_id: int = None) -> List[Dict[str, Any]]:
        """Get custom referral links (all or for specific user) - DEPRECATED: Use CustomReferralService instead"""
        try:
            from services.custom_referral_service import CustomReferralService
            custom_referral_service = CustomReferralService()

            if user_id:
                return await custom_referral_service.get_custom_referrals_by_user(user_id)
            else:
                return await custom_referral_service.get_all_custom_referrals()

        except Exception as e:
            logger.error(f"Error getting custom referral links: {e}")
            return []
    
    async def delete_custom_referral_link(self, custom_param: str) -> bool:
        """Delete custom referral link"""
        try:
            collection = await get_collection(COLLECTIONS['custom_referrals'])
            
            result = await collection.delete_one({"parameter": custom_param})
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error deleting custom referral link {custom_param}: {e}")
            return False
    
    async def edit_custom_referral_link(self, old_param: str, new_param: str) -> bool:
        """Edit custom referral link parameter - DEPRECATED: Use CustomReferralService instead"""
        try:
            from services.custom_referral_service import CustomReferralService
            custom_referral_service = CustomReferralService()
            result = await custom_referral_service.update_custom_referral(old_param, new_param, 0)  # admin_id=0 for legacy calls
            return result.get('success', False)

        except Exception as e:
            logger.error(f"Error editing custom referral link: {e}")
            return False
    
    async def get_referral_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get referral statistics for a user"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            user = await users_collection.find_one({"user_id": user_id})
            
            if not user:
                return {
                    "total_referrals": 0,
                    "total_earnings": 0,
                    "referral_list": []
                }
            
            promotion_report = user.get('promotion_report', [])
            
            return {
                "total_referrals": len(promotion_report),
                "total_earnings": sum(report.get('amount_got', 0) for report in promotion_report),
                "referral_list": promotion_report
            }
            
        except Exception as e:
            logger.error(f"Error getting referral statistics for user {user_id}: {e}")
            return {
                "total_referrals": 0,
                "total_earnings": 0,
                "referral_list": []
            }
    
    async def get_level_bonus_eligibility(self, user_id: int) -> Dict[str, Any]:
        """Check level bonus eligibility for user"""
        try:
            # Get user data
            users_collection = await get_collection(COLLECTIONS['users'])
            user = await users_collection.find_one({"user_id": user_id})
            
            if not user:
                return {"eligible_levels": [], "claimed_levels": []}
            
            # Check if level rewards are enabled using dedicated service
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()

            if not await level_rewards_service.is_level_rewards_enabled():
                return {"eligible_levels": [], "claimed_levels": []}
            
            # Get level rewards configuration using dedicated service
            level_config = await level_rewards_service.get_level_rewards_config()
            referral_requirements = level_config.get('referral_requirements', [])
            bonus_amounts = level_config.get('bonus_amounts', [])
            
            # Get user's referral count and claimed levels
            total_referrals = len(user.get('promotion_report', []))
            claimed_levels = user.get('claimed_levels', [])
            
            # Check eligibility for each level
            eligible_levels = []
            for i, required_referrals in enumerate(referral_requirements):
                level = i + 1
                if (total_referrals >= required_referrals and 
                    level not in claimed_levels and 
                    i < len(bonus_amounts)):
                    
                    eligible_levels.append({
                        "level": level,
                        "required_referrals": required_referrals,
                        "bonus_amount": bonus_amounts[i]
                    })
            
            return {
                "eligible_levels": eligible_levels,
                "claimed_levels": claimed_levels,
                "total_referrals": total_referrals
            }
            
        except Exception as e:
            logger.error(f"Error checking level bonus eligibility for user {user_id}: {e}")
            return {"eligible_levels": [], "claimed_levels": []}
    
    async def claim_level_bonus(self, user_id: int, level: int) -> Optional[int]:
        """Claim level bonus for user"""
        try:
            # Check eligibility
            eligibility = await self.get_level_bonus_eligibility(user_id)
            eligible_level = next(
                (l for l in eligibility['eligible_levels'] if l['level'] == level), 
                None
            )
            
            if not eligible_level:
                return None
            
            bonus_amount = eligible_level['bonus_amount']
            
            # Update user: add bonus to balance and mark level as claimed
            users_collection = await get_collection(COLLECTIONS['users'])
            result = await users_collection.update_one(
                {"user_id": user_id},
                {
                    "$inc": {"balance": bonus_amount},
                    "$push": {"claimed_levels": level},
                    "$set": {"updated_at": get_current_timestamp()}
                }
            )
            
            if result.modified_count > 0:
                logger.info(f"Level {level} bonus claimed by user {user_id}: ₹{bonus_amount}")
                return bonus_amount
            
            return None
            
        except Exception as e:
            logger.error(f"Error claiming level bonus for user {user_id}: {e}")
            return None
    
    async def get_referral_leaderboard(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get referral leaderboard"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            pipeline = [
                {
                    "$addFields": {
                        "total_referrals": {"$size": "$promotion_report"},
                        "total_referral_earnings": {
                            "$sum": "$promotion_report.amount_got"
                        }
                    }
                },
                {
                    "$match": {
                        "total_referrals": {"$gt": 0}
                    }
                },
                {
                    "$sort": {
                        "total_referrals": -1,
                        "total_referral_earnings": -1
                    }
                },
                {
                    "$limit": limit
                },
                {
                    "$project": {
                        "user_id": 1,
                        "first_name": 1,
                        "username": 1,
                        "total_referrals": 1,
                        "total_referral_earnings": 1,
                        "banned": 1
                    }
                }
            ]
            
            cursor = users_collection.aggregate(pipeline)
            leaderboard = await cursor.to_list(length=None)
            return leaderboard
            
        except Exception as e:
            logger.error(f"Error getting referral leaderboard: {e}")
            return []
